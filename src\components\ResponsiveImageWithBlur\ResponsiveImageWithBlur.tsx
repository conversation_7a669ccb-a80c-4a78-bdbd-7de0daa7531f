'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import classNames from '@utils/classNames';
import styles from './ResponsiveImageWithBlur.module.css';

export default function ResponsiveImageWithBlur({
  data,
  mobileData,
  width,
  height,
  fill,
  quality,
  priority,
  unoptimized,
  loading,
  mainClass,
  prefferedSize,
}: {
  data: any;
  mobileData?: any;
  width?: any;
  height?: any;
  fill?: any;
  unoptimized?: any;
  quality?: any;
  priority?: any;
  loading?: any;
  mainClass: any;
  prefferedSize?: 'large' | 'medium' | 'small';
}) {
  const [isImageLoaded, setIsImageLoaded] = useState(false);

  // Helper function to get the best image URL based on preferred size
  const getImageUrl = (imageData: any, preferredSize?: string) => {
    if (!imageData) return null;

    switch (preferredSize) {
      case 'large':
        return (
          imageData?.format?.large?.url ||
          imageData?.formats?.large?.url ||
          imageData?.url
        );
      case 'medium':
        return (
          imageData?.format?.medium?.url ||
          imageData?.formats?.medium?.url ||
          imageData?.format?.large?.url ||
          imageData?.formats?.large?.url ||
          imageData?.url
        );
      case 'small':
        return (
          imageData?.format?.small?.url ||
          imageData?.formats?.small?.url ||
          imageData?.format?.medium?.url ||
          imageData?.formats?.medium?.url ||
          imageData?.format?.large?.url ||
          imageData?.formats?.large?.url ||
          imageData?.url
        );
      default:
        return imageData?.url;
    }
  };

  // Get URLs for desktop and mobile images
  const desktopImageUrl = getImageUrl(data, prefferedSize);
  const mobileImageUrl = mobileData
    ? getImageUrl(mobileData, prefferedSize)
    : null;

  // Use mobile image if available, otherwise fallback to desktop image
  const finalMobileImageUrl = mobileImageUrl || desktopImageUrl;

  // Get thumbnail for blur effect - prefer mobile thumbnail on mobile, fallback to desktop
  const thumbnailUrl =
    mobileData?.formats?.thumbnail?.url || data?.formats?.thumbnail?.url;

  return (
    <>
      {!isImageLoaded && thumbnailUrl && (
        <Image
          src={thumbnailUrl}
          alt={data?.alternativeText || 'Hero image'}
          width={width}
          height={height}
          fill={fill}
          className={classNames(mainClass, 'blur')}
          priority={priority}
          loading={loading}
        />
      )}

      {/* Show mobile and desktop images with CSS-based responsive behavior */}
      {mobileImageUrl && finalMobileImageUrl !== desktopImageUrl ? (
        <>
          {/* Desktop image - hidden on mobile */}
          <Image
            src={desktopImageUrl}
            width={width}
            height={height}
            fill={fill}
            alt={data?.alternativeText || 'Hero Image'}
            className={classNames(
              mainClass,
              styles.desktop_image,
              isImageLoaded ? styles.image_visible : styles.image_hidden,
            )}
            quality={quality}
            priority={priority}
            loading={loading}
            unoptimized={unoptimized}
            onLoad={() => setIsImageLoaded(true)}
          />

          {/* Mobile image - hidden on desktop */}
          <Image
            src={finalMobileImageUrl}
            width={width}
            height={height}
            fill={fill}
            alt={data?.alternativeText || 'Hero Image'}
            className={classNames(
              mainClass,
              styles.mobile_image,
              isImageLoaded ? styles.image_visible : styles.image_hidden,
            )}
            quality={quality}
            priority={priority}
            loading={loading}
            unoptimized={unoptimized}
            onLoad={() => setIsImageLoaded(true)}
          />
        </>
      ) : (
        /* Fallback to single image when no mobile image is available */
        <Image
          src={desktopImageUrl}
          width={width}
          height={height}
          fill={fill}
          alt={data?.alternativeText || 'Hero Image'}
          className={classNames(
            mainClass,
            isImageLoaded ? styles.image_visible : styles.image_hidden,
          )}
          quality={quality}
          priority={priority}
          loading={loading}
          unoptimized={unoptimized}
          onLoad={() => setIsImageLoaded(true)}
        />
      )}
    </>
  );
}
