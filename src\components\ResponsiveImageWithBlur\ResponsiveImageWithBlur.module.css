.image_visible {
  opacity: 1;
  transition: opacity 0.3s ease-in-out;
}

.image_hidden {
  opacity: 0;
  transition: opacity 0.3s ease-in-out;
}

.desktop_image {
  display: block;
}

.mobile_image {
  display: none;
}

/* Show mobile image and hide desktop image on mobile devices */
@media screen and (max-width: 768px) {
  .desktop_image {
    display: none;
  }

  .mobile_image {
    display: block;
  }
}
